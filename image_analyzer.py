"""
Image analysis using Doubao vision model
"""
import base64
import asyncio
from typing import Dict, List, Optional, Any
from pathlib import Path
import httpx
from PIL import Image
import io
import logging

from config import get_settings

logger = logging.getLogger(__name__)

class DoubaoImageAnalyzer:
    """Doubao vision model image analyzer"""
    
    def __init__(self):
        self.settings = get_settings()
        self.client = httpx.AsyncClient(
            base_url=self.settings.doubao_base_url,
            headers={
                "Authorization": f"Bearer {self.settings.doubao_api_key}",
                "Content-Type": "application/json"
            },
            timeout=60.0
        )
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def _encode_image_to_base64(self, image_path: str) -> str:
        """Encode image file to base64 string"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            logger.error(f"Error encoding image {image_path}: {e}")
            raise
    
    def _encode_image_bytes_to_base64(self, image_bytes: bytes) -> str:
        """Encode image bytes to base64 string"""
        return base64.b64encode(image_bytes).decode('utf-8')
    
    def _optimize_image(self, image_path: str, max_size: int = 1024) -> bytes:
        """Optimize image size for API"""
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # Resize if too large
                if max(img.size) > max_size:
                    ratio = max_size / max(img.size)
                    new_size = tuple(int(dim * ratio) for dim in img.size)
                    img = img.resize(new_size, Image.Resampling.LANCZOS)
                
                # Save to bytes
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG', quality=85, optimize=True)
                return buffer.getvalue()
        except Exception as e:
            logger.error(f"Error optimizing image {image_path}: {e}")
            # Fallback to original file
            with open(image_path, "rb") as f:
                return f.read()
    
    async def analyze_image(
        self, 
        image_path: str, 
        prompt: str = "请详细描述这张图片的内容，包括主要元素、颜色、布局和任何文字信息。",
        context: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Analyze a single image using Doubao vision model
        
        Args:
            image_path: Path to the image file
            prompt: Analysis prompt
            context: Additional context about the image
            
        Returns:
            Analysis result dictionary
        """
        try:
            # Optimize and encode image
            image_bytes = self._optimize_image(image_path)
            image_base64 = self._encode_image_bytes_to_base64(image_bytes)
            
            # Prepare the request
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": f"{prompt}\n\n{context if context else ''}"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ]
            
            payload = {
                "model": self.settings.doubao_model_id,
                "messages": messages,
                "max_tokens": 2000,
                "temperature": 0.1
            }
            
            # Make API request
            response = await self.client.post("/chat/completions", json=payload)
            response.raise_for_status()
            
            result = response.json()
            
            if "choices" in result and len(result["choices"]) > 0:
                analysis = result["choices"][0]["message"]["content"]
                return {
                    "success": True,
                    "image_path": image_path,
                    "analysis": analysis,
                    "model": self.settings.doubao_model_id,
                    "prompt": prompt,
                    "context": context,
                    "usage": result.get("usage", {})
                }
            else:
                return {
                    "success": False,
                    "error": "No analysis returned from model",
                    "image_path": image_path
                }
                
        except Exception as e:
            logger.error(f"Error analyzing image {image_path}: {e}")
            return {
                "success": False,
                "error": str(e),
                "image_path": image_path
            }
    
    async def analyze_multiple_images(
        self, 
        image_paths: List[str], 
        prompt: str = "请详细描述这张图片的内容。",
        context: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Analyze multiple images concurrently
        
        Args:
            image_paths: List of image file paths
            prompt: Analysis prompt
            context: Additional context
            
        Returns:
            List of analysis results
        """
        tasks = [
            self.analyze_image(path, prompt, context) 
            for path in image_paths
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "success": False,
                    "error": str(result),
                    "image_path": image_paths[i]
                })
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def analyze_image_with_webpage_context(
        self, 
        image_path: str, 
        webpage_text: str, 
        webpage_url: str
    ) -> Dict[str, Any]:
        """
        Analyze image with webpage context
        
        Args:
            image_path: Path to the image file
            webpage_text: Text content from the webpage
            webpage_url: URL of the webpage
            
        Returns:
            Analysis result with context
        """
        context = f"""
这张图片来自网页: {webpage_url}

网页文本内容摘要:
{webpage_text[:1000]}...

请结合网页内容分析这张图片，说明图片与网页内容的关系，以及图片在网页中的作用和意义。
        """.strip()
        
        prompt = "请详细分析这张图片，并结合提供的网页上下文信息，说明图片的内容、作用和与网页的关系。"
        
        return await self.analyze_image(image_path, prompt, context)
